//
//  BlindBoxBugFixTestView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI

/**
 * 盲盒Bug修复测试视图
 * 测试两个修复的问题：
 * 1. 盲盒配置数据持久化显示
 * 2. 抽奖结果显示在兑换记录中
 */
struct BlindBoxBugFixTestView: View {
    
    @EnvironmentObject private var dataManager: DataManager
    @State private var selectedMember: Member?
    @State private var testResults: [String] = []
    @State private var showBlindBoxConfig = false
    @State private var showBlindBox = false
    @State private var showMemberDetail = false
    
    // 测试配置
    private let testConfig = BlindBoxConfigData(
        boxCount: 3,
        costPerPlay: 20,
        boxPrizes: ["Bug修复奖品A", "Bug修复奖品B", "Bug修复奖品C"]
    )
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("盲盒Bug修复测试")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(.top, 20)
                
                // 测试说明
                testInstructionSection
                
                // 成员选择
                memberSelectionSection
                
                // 测试步骤
                testStepsSection
                
                // 测试结果
                if !testResults.isEmpty {
                    testResultsSection
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .background(Color.gray.opacity(0.05))
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showBlindBoxConfig) {
            if let member = selectedMember {
                BlindBoxConfigPopupView(
                    isPresented: $showBlindBoxConfig,
                    selectedMember: member,
                    onSave: { configData in
                        handleConfigSave(configData)
                    },
                    onCancel: {
                        showBlindBoxConfig = false
                    }
                )
            }
        }
        .fullScreenCover(isPresented: $showBlindBox) {
            if let member = selectedMember {
                NavigationView {
                    BlindBoxView(
                        member: member,
                        onDismiss: {
                            showBlindBox = false
                            addTestResult("✅ 盲盒页面关闭，准备检查兑换记录")
                        },
                        onNavigateToSettings: {
                            showBlindBox = false
                        }
                    )
                }
                .environmentObject(dataManager)
            }
        }
        .fullScreenCover(isPresented: $showMemberDetail) {
            if let member = selectedMember {
                NavigationView {
                    MemberDetailView(member: member)
                        .toolbar {
                            ToolbarItem(placement: .navigationBarTrailing) {
                                Button("关闭") {
                                    showMemberDetail = false
                                }
                            }
                        }
                }
                .environmentObject(dataManager)
            }
        }
    }
    
    // MARK: - Test Instruction Section
    
    private var testInstructionSection: some View {
        VStack(spacing: 12) {
            Text("测试说明")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Bug 1: 配置数据持久化")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text("• 保存盲盒配置后再次打开应显示已配置的数据")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                Text("Bug 2: 抽奖结果记录")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(.top, 8)
                
                Text("• 盲盒抽奖结果应显示在成员详情页的兑换记录中")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - Member Selection Section
    
    private var memberSelectionSection: some View {
        VStack(spacing: 16) {
            Text("选择测试成员")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            if dataManager.members.isEmpty {
                Text("暂无成员，请先添加成员")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .padding(.vertical, 20)
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(dataManager.members, id: \.objectID) { member in
                            memberCard(member: member)
                        }
                    }
                    .padding(.horizontal, 20)
                }
            }
        }
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private func memberCard(member: Member) -> some View {
        VStack(spacing: 8) {
            Circle()
                .fill(selectedMember?.objectID == member.objectID ? DesignSystem.Colors.primary : Color.gray.opacity(0.3))
                .frame(width: 50, height: 50)
                .overlay(
                    Text(String(member.displayName.prefix(1)))
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.white)
                )
            
            Text(member.displayName)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .lineLimit(1)
            
            Text("\(Int(member.currentPoints))积分")
                .font(.system(size: 10, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(selectedMember?.objectID == member.objectID ? DesignSystem.Colors.primary.opacity(0.1) : Color.clear)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(selectedMember?.objectID == member.objectID ? DesignSystem.Colors.primary : Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
        .onTapGesture {
            selectedMember = member
            addTestResult("选择测试成员: \(member.displayName)")
        }
    }
    
    // MARK: - Test Steps Section
    
    private var testStepsSection: some View {
        VStack(spacing: 16) {
            Text("测试步骤")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            VStack(spacing: 12) {
                testStepButton(
                    title: "1. 添加测试积分",
                    description: "为成员添加足够的积分",
                    action: addTestPoints
                )
                
                testStepButton(
                    title: "2. 打开盲盒配置",
                    description: "测试配置界面和数据保存",
                    action: openBlindBoxConfig
                )
                
                testStepButton(
                    title: "3. 再次打开配置",
                    description: "验证配置数据持久化显示",
                    action: reopenBlindBoxConfig
                )
                
                testStepButton(
                    title: "4. 打开盲盒页面",
                    description: "进行抽奖测试",
                    action: openBlindBoxPage
                )
                
                testStepButton(
                    title: "5. 查看成员详情",
                    description: "检查兑换记录是否显示",
                    action: openMemberDetail
                )
            }
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private func testStepButton(
        title: String,
        description: String,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack(spacing: 12) {
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text(description)
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.05))
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(selectedMember == nil)
    }
    
    // MARK: - Test Results Section
    
    private var testResultsSection: some View {
        VStack(spacing: 12) {
            Text("测试结果")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            ScrollView {
                VStack(alignment: .leading, spacing: 8) {
                    ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                        HStack(alignment: .top, spacing: 8) {
                            Text("\(index + 1).")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .frame(width: 20, alignment: .leading)
                            
                            Text(result)
                                .font(.system(size: 12, weight: .regular))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                                .multilineTextAlignment(.leading)
                            
                            Spacer()
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
            }
            .frame(maxHeight: 200)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.green.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.green.opacity(0.3), lineWidth: 1)
                    )
            )
            
            Button(action: {
                testResults.removeAll()
            }) {
                Text("清除结果")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.red)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    // MARK: - Test Actions
    
    private func addTestPoints() {
        guard let member = selectedMember else {
            addTestResult("❌ 未选择成员")
            return
        }
        
        let newPoints = Int(member.currentPoints) + 100
        member.currentPoints = Int32(newPoints)
        dataManager.save()
        
        addTestResult("✅ 为 \(member.displayName) 添加100积分，当前积分: \(newPoints)")
    }
    
    private func openBlindBoxConfig() {
        guard selectedMember != nil else {
            addTestResult("❌ 未选择成员")
            return
        }
        
        showBlindBoxConfig = true
        addTestResult("📝 打开盲盒配置界面")
    }
    
    private func reopenBlindBoxConfig() {
        guard let member = selectedMember else {
            addTestResult("❌ 未选择成员")
            return
        }
        
        // 检查是否有配置
        if let config = dataManager.getBlindBoxConfig(for: member) {
            addTestResult("✅ 检测到已保存的配置: 盲盒数=\(config.itemCount), 积分=\(config.costPerPlay)")
            showBlindBoxConfig = true
            addTestResult("📝 再次打开配置界面，验证数据持久化")
        } else {
            addTestResult("❌ 未找到已保存的配置，请先完成配置保存")
        }
    }
    
    private func openBlindBoxPage() {
        guard let member = selectedMember else {
            addTestResult("❌ 未选择成员")
            return
        }
        
        if dataManager.getBlindBoxConfig(for: member) != nil {
            showBlindBox = true
            addTestResult("🎁 打开盲盒页面，请进行抽奖测试")
        } else {
            addTestResult("❌ 未找到盲盒配置，请先完成配置")
        }
    }
    
    private func openMemberDetail() {
        guard let member = selectedMember else {
            addTestResult("❌ 未选择成员")
            return
        }
        
        // 检查兑换记录
        let redemptionRecords = member.allRedemptionRecords
        let blindBoxRecords = redemptionRecords.filter { $0.source == "盲盒抽奖" }
        
        if !blindBoxRecords.isEmpty {
            addTestResult("✅ 检测到\(blindBoxRecords.count)条盲盒抽奖记录")
            for record in blindBoxRecords {
                addTestResult("   - 奖品: \(record.prizeName ?? "未知"), 消耗: \(record.cost)积分")
            }
        } else {
            addTestResult("⚠️ 未检测到盲盒抽奖记录，请先进行抽奖")
        }
        
        showMemberDetail = true
        addTestResult("👤 打开成员详情页，请查看兑换记录")
    }
    
    private func handleConfigSave(_ configData: BlindBoxConfigData) {
        guard let member = selectedMember else { return }
        
        let savedConfig = dataManager.saveBlindBoxConfig(
            for: member,
            boxCount: configData.boxCount,
            costPerPlay: configData.costPerPlay,
            boxPrizes: configData.boxPrizes
        )
        
        showBlindBoxConfig = false
        
        if savedConfig != nil {
            addTestResult("✅ 配置保存成功: 盲盒数=\(configData.boxCount), 积分=\(configData.costPerPlay)")
            addTestResult("   奖品: \(configData.boxPrizes.joined(separator: "、"))")
        } else {
            addTestResult("❌ 配置保存失败")
        }
    }
    
    private func addTestResult(_ result: String) {
        testResults.append(result)
    }
}

#Preview {
    BlindBoxBugFixTestView()
        .environmentObject(DataManager.shared)
}
