//
//  BlindBoxViewModel.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI
import Combine
import Foundation

/**
 * 盲盒视图模型
 * 管理盲盒配置加载、状态管理、开箱逻辑和动画控制
 */
@MainActor
class BlindBoxViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var boxItems: [BlindBoxItem] = []
    @Published var isLoading = false
    @Published var showResult = false
    @Published var resultPrize = ""
    @Published var showInsufficientPoints = false
    @Published var showNoConfig = false
    @Published var particles: [ParticleItem] = []
    
    // MARK: - Private Properties
    private let member: Member
    private let dataManager = DataManager.shared
    private var blindBoxConfig: LotteryConfig?
    private var cancellables = Set<AnyCancellable>()
    private var particleTimer: Timer?
    
    // MARK: - Animation States
    @Published var animationStates: [UUID: ExplosionState] = [:]
    @Published var floatingTrigger = false
    
    // MARK: - Constants
    private let maxParticles = 50
    private let particleLifespan: Double = 1.5
    private let explosionDuration: Double = 1.1
    
    // MARK: - Computed Properties
    
    /**
     * 获取每次开启消耗的积分
     */
    var costPerOpen: Int {
        return Int(blindBoxConfig?.costPerPlay ?? 10)
    }
    
    /**
     * 检查成员是否有足够积分开启盲盒
     */
    var canAffordOpening: Bool {
        return Int(member.currentPoints) >= costPerOpen
    }
    
    /**
     * 获取未开启的盲盒数量
     */
    var unopenedCount: Int {
        return boxItems.filter { !$0.isOpened }.count
    }
    
    /**
     * 检查是否所有盲盒都已开启
     */
    var allBoxesOpened: Bool {
        return boxItems.allSatisfy { $0.isOpened }
    }
    
    // MARK: - Initialization
    
    init(member: Member) {
        self.member = member
        setupFloatingAnimation()
    }
    
    // MARK: - Public Methods
    
    /**
     * 加载盲盒配置
     */
    func loadBlindBoxConfig() {
        isLoading = true
        
        // 获取成员的盲盒配置
        if let config = dataManager.getBlindBoxConfig(for: member) {
            blindBoxConfig = config
            generateBoxItems(from: config)
            showNoConfig = false
        } else {
            blindBoxConfig = nil
            boxItems = []
            showNoConfig = true
        }
        
        isLoading = false
    }
    
    /**
     * 开启指定索引的盲盒
     */
    func openBlindBox(at index: Int) -> Bool {
        guard index < boxItems.count else { return false }
        guard boxItems[index].isClickable else { return false }
        guard canAffordOpening else {
            showInsufficientPoints = true
            return false
        }
        
        // 开始动画
        boxItems[index].startOpening()
        animationStates[boxItems[index].id] = .exploding
        
        // 随机选择奖品
        let selectedPrize = getRandomPrize()
        
        // 执行开箱动画序列
        executeOpeningAnimation(at: index, prize: selectedPrize)
        
        return true
    }
    
    /**
     * 确认开箱结果
     */
    func confirmResult() {
        showResult = false
        resultPrize = ""
    }
    
    /**
     * 重置所有盲盒状态
     */
    func resetAllBoxes() {
        for i in 0..<boxItems.count {
            boxItems[i].resetAnimation()
            boxItems[i].isOpened = false
        }
        animationStates.removeAll()
        particles.removeAll()
        stopParticleAnimation()
    }
    
    /**
     * 刷新数据
     */
    func refresh() {
        loadBlindBoxConfig()
    }
}

// MARK: - Private Methods

extension BlindBoxViewModel {
    
    /**
     * 从配置生成盲盒项目
     */
    private func generateBoxItems(from config: LotteryConfig) {
        let itemCount = Int(config.itemCount)
        let sortedItems = config.allItems
        
        var newBoxItems: [BlindBoxItem] = []
        
        for i in 0..<itemCount {
            let prizeName: String
            if let item = sortedItems.first(where: { $0.itemIndex == i }) {
                prizeName = item.formattedPrizeName
            } else {
                prizeName = "神秘奖品"
            }
            
            let boxItem = BlindBoxItem.create(index: i, prizeName: prizeName)
            newBoxItems.append(boxItem)
        }
        
        boxItems = newBoxItems
        print("✅ 生成盲盒项目: \(itemCount)个")
    }
    
    /**
     * 获取随机奖品
     */
    private func getRandomPrize() -> String {
        guard let config = blindBoxConfig else { return "神秘奖品" }
        
        let allItems = config.allItems
        guard !allItems.isEmpty else { return "神秘奖品" }
        
        let randomItem = allItems.randomElement()
        return randomItem?.formattedPrizeName ?? "神秘奖品"
    }
    
    /**
     * 扣除开启积分
     */
    private func deductPointsForOpening() -> Bool {
        let currentPoints = Int(member.currentPoints)
        guard currentPoints >= costPerOpen else { return false }

        let newPoints = currentPoints - costPerOpen
        member.currentPoints = Int32(newPoints)
        dataManager.save()

        return true
    }
    
    /**
     * 创建抽奖记录
     */
    private func createLotteryRecord(prize: String, cost: Int) {
        dataManager.createLotteryRecord(
            for: member,
            toolType: "blindbox",
            prizeResult: prize,
            cost: Int32(cost)
        )
    }
    
    /**
     * 执行开箱动画序列
     */
    private func executeOpeningAnimation(at index: Int, prize: String) {
        let boxId = boxItems[index].id
        
        // 阶段1: 预备放大 (0.1秒)
        withAnimation(.easeOut(duration: 0.1)) {
            boxItems[index].scaleEffect = 1.2
        }
        
        // 阶段2: 爆炸分解 (0.3秒)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeInOut(duration: 0.3)) {
                self.animationStates[boxId] = .exploding
            }
            
            // 生成粒子
            self.generateParticles(at: self.boxItems[index].position)
        }
        
        // 阶段3: 显示奖品并立即生成记录 (0.2秒)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation(.easeIn(duration: 0.2)) {
                // 更新盲盒显示的奖品名称为实际获得的奖品
                self.boxItems[index] = BlindBoxItem.createWithPrize(
                    index: self.boxItems[index].index,
                    prizeName: prize,
                    isOpened: true,
                    explosionState: .completed
                )
                self.animationStates[boxId] = .completed
            }
            
            // 立即扣除积分并生成记录
            let success = self.deductPointsForOpening()
            if success {
                // 创建抽奖记录
                self.createLotteryRecord(prize: prize, cost: self.costPerOpen)
                print("✅ 盲盒开启成功: 获得 \(prize)，消耗 \(self.costPerOpen) 积分")
            } else {
                print("❌ 盲盒开启失败: 积分扣除失败")
            }
        }
        
        // 阶段4: 显示结果弹窗 (0.3秒后)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.7) {
            self.resultPrize = prize
            self.showResult = true
        }
    }

    /**
     * 生成粒子效果
     */
    private func generateParticles(at position: CGPoint) {
        let particleCount = Int.random(in: 30...maxParticles)
        let colors: [Color] = [.yellow, .orange, .pink, .purple, .blue, .green]

        for _ in 0..<particleCount {
            let angle = Double.random(in: 0...(2 * .pi))
            let speed = CGFloat.random(in: 150...300)
            let velocity = CGPoint(
                x: Foundation.cos(angle) * speed,
                y: Foundation.sin(angle) * speed
            )

            let particle = ParticleItem(
                startPosition: position,
                currentPosition: position,
                velocity: velocity,
                color: colors.randomElement() ?? .yellow,
                size: CGFloat.random(in: 4...8),
                lifespan: particleLifespan
            )

            particles.append(particle)
        }

        startParticleAnimation()
    }

    /**
     * 开始粒子动画
     */
    private func startParticleAnimation() {
        guard particleTimer == nil else { return }

        particleTimer = Timer.scheduledTimer(withTimeInterval: 1.0 / 60.0, repeats: true) { _ in
            Task { @MainActor in
                self.updateParticles()
            }
        }
    }

    /**
     * 停止粒子动画
     */
    private func stopParticleAnimation() {
        particleTimer?.invalidate()
        particleTimer = nil
    }

    /**
     * 更新粒子位置
     */
    private func updateParticles() {
        let deltaTime = 1.0 / 60.0
        let gravity: CGFloat = 500

        for i in particles.indices {
            particles[i].age += deltaTime

            // 更新位置（考虑重力）
            particles[i].currentPosition.x += particles[i].velocity.x * CGFloat(deltaTime)
            particles[i].currentPosition.y += particles[i].velocity.y * CGFloat(deltaTime) + 0.5 * gravity * CGFloat(deltaTime * deltaTime)
        }

        // 移除过期粒子
        particles.removeAll { !$0.isAlive }

        // 如果没有粒子了，停止动画
        if particles.isEmpty {
            stopParticleAnimation()
        }
    }

    /**
     * 设置悬浮动画
     */
    private func setupFloatingAnimation() {
        Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { _ in
            Task { @MainActor in
                withAnimation(.easeInOut(duration: 2.0)) {
                    self.floatingTrigger.toggle()
                }
            }
        }
    }
}
