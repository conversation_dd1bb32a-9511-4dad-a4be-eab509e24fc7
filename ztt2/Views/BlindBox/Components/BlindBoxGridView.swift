//
//  BlindBoxGridView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI

/**
 * 盲盒网格视图组件
 * 负责展示多个盲盒的网格布局，支持悬浮动画和交互
 */
struct BlindBoxGridView: View {
    
    // MARK: - Properties
    @ObservedObject var viewModel: BlindBoxViewModel
    let onBoxTapped: (Int) -> Void
    
    // MARK: - Grid Configuration
    private let columns = 2
    private let spacing: CGFloat = 20
    private let horizontalPadding: CGFloat = 25
    
    // MARK: - Animation State
    @State private var gridAppeared = false
    
    // MARK: - Computed Properties
    
    private var gridColumns: [GridItem] {
        Array(repeating: GridItem(.flexible(), spacing: spacing), count: columns)
    }
    
    private var itemSize: CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        let totalHorizontalPadding = horizontalPadding * 2
        let totalSpacing = spacing * CGFloat(columns - 1)
        let availableWidth = screenWidth - totalHorizontalPadding - totalSpacing
        return availableWidth / CGFloat(columns)
    }
    
    var body: some View {
        ScrollView(.vertical, showsIndicators: false) {
            LazyVGrid(columns: gridColumns, spacing: spacing) {
                ForEach(Array(viewModel.boxItems.enumerated()), id: \.element.id) { index, boxItem in
                    blindBoxItemView(boxItem: boxItem, index: index)
                        .opacity(gridAppeared ? 1.0 : 0.0)
                        .offset(y: gridAppeared ? 0 : 30)
                        .animation(
                            .easeOut(duration: 0.6)
                            .delay(Double(index) * 0.1),
                            value: gridAppeared
                        )
                }
            }
            .padding(.horizontal, horizontalPadding)
            .padding(.top, 20)
            .padding(.bottom, 40)
        }
        .onAppear {
            withAnimation {
                gridAppeared = true
            }
        }
        .onDisappear {
            gridAppeared = false
        }
    }
    
    // MARK: - Blind Box Item View
    
    /**
     * 单个盲盒项目视图
     */
    private func blindBoxItemView(boxItem: BlindBoxItem, index: Int) -> some View {
        VStack(spacing: 8) {
            // 盲盒3D立方体
            BlindBoxCubeView(
                boxItem: boxItem,
                size: itemSize,
                onTap: {
                    onBoxTapped(index)
                }
            )
            .floating(
                offset: CGFloat.random(in: 6...10),
                rotation: Double.random(in: 2...6),
                delay: Double(index) * 0.15,
                duration: Double.random(in: 1.8...2.4),
                enabled: !boxItem.isOpened && boxItem.explosionState == .idle
            )
            
            // 盲盒标题和状态
            blindBoxInfoView(boxItem: boxItem, index: index)
        }
        .contentShape(Rectangle()) // 扩大点击区域
    }
    
    /**
     * 盲盒信息视图
     */
    private func blindBoxInfoView(boxItem: BlindBoxItem, index: Int) -> some View {
        VStack(spacing: 4) {
            // 盲盒标题
            Text(boxItem.displayTitle)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            // 状态指示器
            HStack(spacing: 4) {
                Circle()
                    .fill(boxItem.isOpened ? Color.green : Color.orange)
                    .frame(width: 6, height: 6)
                
                Text(boxItem.isOpened ? "blind_box.status.opened".localized : "blind_box.status.unopened".localized)
                    .font(.system(size: 11, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            // 奖品名称（已开启时显示）
            if boxItem.isOpened {
                Text(boxItem.prizeName)
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(Color.green)
                    .lineLimit(1)
                    .truncationMode(.tail)
            }
        }
    }
}

/**
 * 盲盒统计信息视图
 */
struct BlindBoxStatsView: View {
    
    @ObservedObject var viewModel: BlindBoxViewModel
    
    var body: some View {
        HStack(spacing: 20) {
            // 总数统计
            statItem(
                icon: "shippingbox",
                title: "blind_box.stats.total".localized,
                value: "\(viewModel.boxItems.count)",
                color: .blue
            )

            // 未开启统计
            statItem(
                icon: "questionmark.circle",
                title: "blind_box.stats.unopened".localized,
                value: "\(viewModel.unopenedCount)",
                color: .orange
            )

            // 已开启统计
            statItem(
                icon: "checkmark.circle.fill",
                title: "blind_box.stats.opened".localized,
                value: "\(viewModel.boxItems.count - viewModel.unopenedCount)",
                color: .green
            )

            // 消耗积分
            statItem(
                icon: "star.fill",
                title: "blind_box.stats.cost_per_open".localized,
                value: "\(viewModel.costPerOpen)",
                color: .purple
            )
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    /**
     * 单个统计项目
     */
    private func statItem(icon: String, title: String, value: String, color: Color) -> some View {
        VStack(spacing: 6) {
            // 图标
            Image(systemName: icon)
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(color)
            
            // 数值
            Text(value)
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            // 标题
            Text(title)
                .font(.system(size: 11, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity)
    }
}

/**
 * 粒子效果视图
 */
struct ParticleEffectView: View {
    
    @ObservedObject var viewModel: BlindBoxViewModel
    
    var body: some View {
        ZStack {
            ForEach(viewModel.particles) { particle in
                Circle()
                    .fill(particle.color)
                    .frame(width: particle.size, height: particle.size)
                    .position(x: particle.currentPosition.x, y: particle.currentPosition.y)
                    .opacity(particle.opacity)
                    .blur(radius: 1)
            }
        }
        .allowsHitTesting(false) // 不拦截触摸事件
    }
}

/**
 * 空状态视图
 */
struct BlindBoxEmptyStateView: View {
    
    let onConfigureTapped: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            // 空状态图标
            Image(systemName: "shippingbox")
                .font(.system(size: 60, weight: .light))
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            // 提示文字
            VStack(spacing: 8) {
                Text("blind_box.no_config_title".localized)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text("blind_box.no_config_message".localized)
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
            
            // 配置按钮
            Button(action: onConfigureTapped) {
                HStack(spacing: 8) {
                    Image(systemName: "gear")
                        .font(.system(size: 14, weight: .medium))
                    
                    Text("blind_box.configure_button".localized)
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(DesignSystem.Colors.primary)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 40)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#Preview {
    let member = Member()
    member.name = "测试成员"
    member.currentPoints = 100

    let viewModel = BlindBoxViewModel(member: member)

    VStack {
        BlindBoxStatsView(viewModel: viewModel)
            .padding()

        BlindBoxGridView(viewModel: viewModel) { index in
            print("点击盲盒: \(index)")
        }
    }
    .background(Color.gray.opacity(0.1))
}
