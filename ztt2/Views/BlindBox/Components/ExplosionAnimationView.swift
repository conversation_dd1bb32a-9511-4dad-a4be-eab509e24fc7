//
//  ExplosionAnimationView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI

/**
 * 爆炸动画组件
 * 实现盲盒开启时的分解、爆炸和视觉效果系统
 */
struct ExplosionAnimationView: View {
    
    // MARK: - Properties
    let isTriggered: Bool
    let explosionCenter: CGPoint
    let onComplete: () -> Void
    
    // MARK: - Animation States
    @State private var faces: [CubeFace] = []
    @State private var explosionProgress: Double = 0
    @State private var showFlash = false
    @State private var showShockwave = false
    @State private var animationCompleted = false
    
    // MARK: - Constants
    private let maxDistance: CGFloat = 200
    private let explosionDuration: Double = 1.0
    private let faceCount = 6
    
    var body: some View {
        ZStack {
            // 立方体面片动画
            ForEach(faces) { face in
                cubeFaceView(face: face)
            }
            
            // 白色闪光效果
            if showFlash {
                flashOverlay
            }
            
            // 冲击波效果
            if showShockwave {
                shockwaveOverlay
            }
        }
        .onChange(of: isTriggered) { triggered in
            if triggered && !animationCompleted {
                startExplosionAnimation()
            }
        }
        .onDisappear {
            resetAnimation()
        }
    }
    
    // MARK: - Cube Face View
    
    private func cubeFaceView(face: CubeFace) -> some View {
        RoundedRectangle(cornerRadius: 8)
            .fill(faceGradient(for: face.type))
            .frame(width: 40, height: 40)
            .rotation3DEffect(
                .degrees(face.rotation),
                axis: (x: 1, y: 1, z: 0)
            )
            .scaleEffect(face.scale)
            .offset(x: face.offset.x, y: face.offset.y)
            .opacity(max(0, 1 - explosionProgress))
    }
    
    // MARK: - Face Gradient
    
    private func faceGradient(for faceType: CubeFace.FaceType) -> LinearGradient {
        LinearGradient(
            colors: [
                faceType.color,
                faceType.color.opacity(0.7)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    // MARK: - Flash Overlay
    
    /**
     * 白色闪光效果
     */
    private var flashOverlay: some View {
        Circle()
            .fill(Color.white.opacity(0.9))
            .frame(width: 100, height: 100)
            .position(x: explosionCenter.x, y: explosionCenter.y)
            .scaleEffect(showFlash ? 2.0 : 0.1)
            .opacity(showFlash ? 0.8 : 0)
            .blur(radius: 8)
            .animation(.easeOut(duration: 0.2), value: showFlash)
    }
    
    /**
     * 冲击波效果
     */
    private var shockwaveOverlay: some View {
        ZStack {
            // 主冲击波
            Circle()
                .stroke(Color.white.opacity(0.7), lineWidth: 3)
                .scaleEffect(explosionProgress * 4)
                .opacity(1 - explosionProgress)
            
            // 次冲击波
            Circle()
                .stroke(Color.yellow.opacity(0.5), lineWidth: 2)
                .scaleEffect(explosionProgress * 6)
                .opacity(max(0, 0.8 - explosionProgress))
        }
        .position(x: explosionCenter.x, y: explosionCenter.y)
        .animation(.easeOut(duration: explosionDuration), value: explosionProgress)
    }
    
    // MARK: - Animation Methods
    
    /**
     * 开始爆炸动画
     */
    private func startExplosionAnimation() {
        guard !animationCompleted else { return }
        
        // 初始化立方体面片
        generateCubeFaces()
        
        // 动画序列
        executeExplosionSequence()
    }
    
    /**
     * 生成立方体面片
     */
    private func generateCubeFaces() {
        faces = CubeFace.FaceType.allCases.map { faceType in
            CubeFace(
                type: faceType,
                offset: .zero,
                rotation: 0,
                scale: 1.0
            )
        }
    }
    
    /**
     * 执行爆炸动画序列
     */
    private func executeExplosionSequence() {
        // 阶段1: 闪光效果 (0.1秒)
        withAnimation(.easeOut(duration: 0.1)) {
            showFlash = true
        }
        
        // 阶段2: 开始面片飞散 (0.3秒)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeOut(duration: 0.3)) {
                self.scatterFaces()
                self.explosionProgress = 0.4
            }
            
            // 显示冲击波
            self.showShockwave = true
        }
        
        // 阶段3: 继续扩散 (0.4秒)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation(.easeInOut(duration: 0.4)) {
                self.continueFaceScattering()
                self.explosionProgress = 0.8
            }
        }
        
        // 阶段4: 消失 (0.3秒)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
            withAnimation(.easeIn(duration: 0.3)) {
                self.explosionProgress = 1.0
                self.hideFaces()
            }
        }
        
        // 阶段5: 完成回调
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.1) {
            self.animationCompleted = true
            self.onComplete()
        }
    }
    
    /**
     * 分散面片
     */
    private func scatterFaces() {
        for i in faces.indices {
            let angle = Double(i) * (2 * .pi / Double(faceCount))
            let distance = CGFloat.random(in: 50...100)
            
            faces[i].offset = CGPoint(
                x: cos(angle) * distance,
                y: sin(angle) * distance
            )
            faces[i].rotation = Double.random(in: 0...360)
            faces[i].scale = CGFloat.random(in: 0.8...1.2)
        }
    }
    
    /**
     * 继续面片扩散
     */
    private func continueFaceScattering() {
        for i in faces.indices {
            let currentOffset = faces[i].offset
            let multiplier = CGFloat.random(in: 1.5...2.5)
            
            faces[i].offset = CGPoint(
                x: currentOffset.x * multiplier,
                y: currentOffset.y * multiplier
            )
            faces[i].rotation += Double.random(in: 180...360)
            faces[i].scale *= CGFloat.random(in: 0.5...0.8)
        }
    }
    
    /**
     * 隐藏面片
     */
    private func hideFaces() {
        for i in faces.indices {
            faces[i].scale = 0
        }
    }
    
    /**
     * 重置动画状态
     */
    private func resetAnimation() {
        faces.removeAll()
        explosionProgress = 0
        showFlash = false
        showShockwave = false
        animationCompleted = false
    }
}

/**
 * 简化的爆炸动画组件
 * 用于更简单的场景
 */
struct SimpleExplosionView: View {
    
    let isTriggered: Bool
    let center: CGPoint
    let onComplete: () -> Void
    
    @State private var explosionScale: CGFloat = 0
    @State private var explosionOpacity: Double = 0
    
    var body: some View {
        ZStack {
            // 主爆炸圆环
            Circle()
                .stroke(Color.white, lineWidth: 4)
                .scaleEffect(explosionScale)
                .opacity(explosionOpacity)
                .position(x: center.x, y: center.y)
            
            // 内圈光晕
            Circle()
                .fill(Color.white.opacity(0.6))
                .scaleEffect(explosionScale * 0.5)
                .opacity(explosionOpacity * 0.7)
                .position(x: center.x, y: center.y)
                .blur(radius: 4)
        }
        .onChange(of: isTriggered) { triggered in
            if triggered {
                startSimpleExplosion()
            }
        }
    }
    
    private func startSimpleExplosion() {
        withAnimation(.easeOut(duration: 0.6)) {
            explosionScale = 3.0
            explosionOpacity = 1.0
        }
        
        withAnimation(.easeIn(duration: 0.4).delay(0.2)) {
            explosionOpacity = 0
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            explosionScale = 0
            onComplete()
        }
    }
}

#Preview {
    ZStack {
        Color.black.opacity(0.3)
            .ignoresSafeArea()
        
        ExplosionAnimationView(
            isTriggered: true,
            explosionCenter: CGPoint(x: 200, y: 300),
            onComplete: {}
        )
    }
}
