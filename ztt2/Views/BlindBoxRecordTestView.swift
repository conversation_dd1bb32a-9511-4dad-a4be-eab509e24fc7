//
//  BlindBoxRecordTestView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI

/**
 * 盲盒记录显示测试视图
 * 验证抽奖记录正确显示在兑换记录中
 */
struct BlindBoxRecordTestView: View {
    
    @EnvironmentObject private var dataManager: DataManager
    @State private var selectedMember: Member?
    @State private var testResults: [String] = []
    @State private var showBlindBox = false
    @State private var showMemberDetail = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("盲盒记录显示测试")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(.top, 20)
                
                // 测试说明
                testInstructionSection
                
                // 成员选择
                memberSelectionSection
                
                // 测试步骤
                testStepsSection
                
                // 测试结果
                if !testResults.isEmpty {
                    testResultsSection
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .background(Color.gray.opacity(0.05))
            .navigationBarHidden(true)
        }
        .fullScreenCover(isPresented: $showBlindBox) {
            if let member = selectedMember {
                NavigationView {
                    BlindBoxView(
                        member: member,
                        onDismiss: {
                            showBlindBox = false
                            checkRecordsAfterLottery()
                        },
                        onNavigateToSettings: {
                            showBlindBox = false
                        }
                    )
                }
                .environmentObject(dataManager)
            }
        }
        .fullScreenCover(isPresented: $showMemberDetail) {
            if let member = selectedMember {
                NavigationView {
                    MemberDetailView(
                        memberId: member.id?.uuidString,
                        onClose: {
                            showMemberDetail = false
                        },
                        onNavigateToSubscription: {
                            showMemberDetail = false
                        }
                    )
                }
                .environmentObject(dataManager)
            }
        }
    }
    
    // MARK: - Test Instruction Section
    
    private var testInstructionSection: some View {
        VStack(spacing: 12) {
            Text("测试说明")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("验证抽奖记录显示逻辑：")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text("• 抽奖记录应该直接显示在兑换记录中")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                Text("• 不需要创建额外的兑换记录")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                Text("• 记录应显示来源（大转盘、盲盒、刮刮卡）")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - Member Selection Section
    
    private var memberSelectionSection: some View {
        VStack(spacing: 16) {
            Text("选择测试成员")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            if dataManager.members.isEmpty {
                Text("暂无成员，请先添加成员")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .padding(.vertical, 20)
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(dataManager.members, id: \.objectID) { member in
                            memberCard(member: member)
                        }
                    }
                    .padding(.horizontal, 20)
                }
            }
        }
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private func memberCard(member: Member) -> some View {
        VStack(spacing: 8) {
            Circle()
                .fill(selectedMember?.objectID == member.objectID ? DesignSystem.Colors.primary : Color.gray.opacity(0.3))
                .frame(width: 50, height: 50)
                .overlay(
                    Text(String(member.displayName.prefix(1)))
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.white)
                )
            
            Text(member.displayName)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .lineLimit(1)
            
            Text("\(Int(member.currentPoints))积分")
                .font(.system(size: 10, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(selectedMember?.objectID == member.objectID ? DesignSystem.Colors.primary.opacity(0.1) : Color.clear)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(selectedMember?.objectID == member.objectID ? DesignSystem.Colors.primary : Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
        .onTapGesture {
            selectedMember = member
            addTestResult("选择测试成员: \(member.displayName)")
        }
    }
    
    // MARK: - Test Steps Section
    
    private var testStepsSection: some View {
        VStack(spacing: 16) {
            Text("测试步骤")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            VStack(spacing: 12) {
                testStepButton(
                    title: "1. 添加测试积分",
                    description: "为成员添加足够的积分",
                    action: addTestPoints
                )
                
                testStepButton(
                    title: "2. 配置盲盒",
                    description: "快速配置盲盒用于测试",
                    action: configureBlindBox
                )
                
                testStepButton(
                    title: "3. 检查记录（抽奖前）",
                    description: "查看当前的记录状态",
                    action: checkRecordsBeforeLottery
                )
                
                testStepButton(
                    title: "4. 进行盲盒抽奖",
                    description: "打开盲盒页面进行抽奖",
                    action: openBlindBoxForLottery
                )
                
                testStepButton(
                    title: "5. 查看成员详情",
                    description: "检查抽奖记录是否正确显示",
                    action: openMemberDetailToCheck
                )
            }
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private func testStepButton(
        title: String,
        description: String,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack(spacing: 12) {
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text(description)
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.05))
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(selectedMember == nil)
    }
    
    // MARK: - Test Results Section
    
    private var testResultsSection: some View {
        VStack(spacing: 12) {
            Text("测试结果")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            ScrollView {
                VStack(alignment: .leading, spacing: 8) {
                    ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                        HStack(alignment: .top, spacing: 8) {
                            Text("\(index + 1).")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .frame(width: 20, alignment: .leading)
                            
                            Text(result)
                                .font(.system(size: 12, weight: .regular))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                                .multilineTextAlignment(.leading)
                            
                            Spacer()
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
            }
            .frame(maxHeight: 200)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.green.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.green.opacity(0.3), lineWidth: 1)
                    )
            )
            
            Button(action: {
                testResults.removeAll()
            }) {
                Text("清除结果")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.red)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    // MARK: - Test Actions
    
    private func addTestPoints() {
        guard let member = selectedMember else {
            addTestResult("❌ 未选择成员")
            return
        }
        
        let newPoints = Int(member.currentPoints) + 100
        member.currentPoints = Int32(newPoints)
        dataManager.save()
        
        addTestResult("✅ 为 \(member.displayName) 添加100积分，当前积分: \(newPoints)")
    }
    
    private func configureBlindBox() {
        guard let member = selectedMember else {
            addTestResult("❌ 未选择成员")
            return
        }
        
        // 快速配置盲盒
        let savedConfig = dataManager.saveBlindBoxConfig(
            for: member,
            boxCount: 3,
            costPerPlay: 20,
            boxPrizes: ["测试奖品A", "测试奖品B", "测试奖品C"]
        )
        
        if savedConfig != nil {
            addTestResult("✅ 快速配置盲盒成功: 3个盲盒，每次消耗20积分")
        } else {
            addTestResult("❌ 盲盒配置失败")
        }
    }
    
    private func checkRecordsBeforeLottery() {
        guard let member = selectedMember else {
            addTestResult("❌ 未选择成员")
            return
        }
        
        let lotteryRecords = member.allLotteryRecords
        let redemptionRecords = member.allRedemptionRecords
        
        addTestResult("📊 抽奖前记录统计:")
        addTestResult("   抽奖记录: \(lotteryRecords.count)条")
        addTestResult("   兑换记录: \(redemptionRecords.count)条")
        
        let blindBoxRecords = lotteryRecords.filter { $0.toolType == "blindbox" }
        addTestResult("   盲盒抽奖记录: \(blindBoxRecords.count)条")
    }
    
    private func openBlindBoxForLottery() {
        guard let member = selectedMember else {
            addTestResult("❌ 未选择成员")
            return
        }
        
        if dataManager.getBlindBoxConfig(for: member) != nil {
            showBlindBox = true
            addTestResult("🎁 打开盲盒页面，请进行抽奖测试")
        } else {
            addTestResult("❌ 未找到盲盒配置，请先配置盲盒")
        }
    }
    
    private func checkRecordsAfterLottery() {
        guard let member = selectedMember else { return }
        
        let lotteryRecords = member.allLotteryRecords
        let redemptionRecords = member.allRedemptionRecords
        
        addTestResult("📊 抽奖后记录统计:")
        addTestResult("   抽奖记录: \(lotteryRecords.count)条")
        addTestResult("   兑换记录: \(redemptionRecords.count)条")
        
        let blindBoxRecords = lotteryRecords.filter { $0.toolType == "blindbox" }
        addTestResult("   盲盒抽奖记录: \(blindBoxRecords.count)条")
        
        if !blindBoxRecords.isEmpty {
            for record in blindBoxRecords {
                addTestResult("   - 奖品: \(record.prizeResult ?? "未知"), 消耗: \(record.cost)积分")
            }
        }
    }
    
    private func openMemberDetailToCheck() {
        guard selectedMember != nil else {
            addTestResult("❌ 未选择成员")
            return
        }
        
        showMemberDetail = true
        addTestResult("👤 打开成员详情页，请查看兑换记录中的抽奖记录")
    }
    
    private func addTestResult(_ result: String) {
        testResults.append(result)
    }
}

#Preview {
    BlindBoxRecordTestView()
        .environmentObject(DataManager.shared)
}
