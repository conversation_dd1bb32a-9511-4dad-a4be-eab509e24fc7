//
//  BlindBoxTestView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI

/**
 * 盲盒功能测试视图
 * 用于测试盲盒动画效果、交互功能和性能表现
 */
struct BlindBoxTestView: View {
    
    @EnvironmentObject private var dataManager: DataManager
    @State private var showBlindBox = false
    @State private var selectedMember: Member?
    @State private var testMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("盲盒功能测试")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(.top, 20)
                
                // 测试说明
                VStack(spacing: 12) {
                    Text("测试说明")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text("1. 选择一个成员进行盲盒测试\n2. 确保成员有足够积分\n3. 测试盲盒开启动画和交互")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .lineSpacing(4)
                }
                .padding(.horizontal, 20)
                
                // 成员选择区域
                memberSelectionSection
                
                // 测试按钮区域
                testButtonsSection
                
                // 测试结果显示
                if !testMessage.isEmpty {
                    testResultSection
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .background(Color.gray.opacity(0.05))
            .navigationBarHidden(true)
        }
        .fullScreenCover(isPresented: $showBlindBox) {
            if let member = selectedMember {
                NavigationView {
                    BlindBoxView(
                        member: member,
                        onDismiss: {
                            showBlindBox = false
                            testMessage = "盲盒测试完成"
                        },
                        onNavigateToSettings: {
                            testMessage = "导航到设置页面"
                        }
                    )
                }
                .environmentObject(dataManager)
            }
        }
    }
    
    // MARK: - Member Selection Section
    
    private var memberSelectionSection: some View {
        VStack(spacing: 16) {
            Text("选择测试成员")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(dataManager.members, id: \.objectID) { member in
                        memberCard(member: member)
                    }
                }
                .padding(.horizontal, 20)
            }
            
            if dataManager.members.isEmpty {
                Text("暂无成员，请先添加成员")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .padding(.vertical, 20)
            }
        }
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private func memberCard(member: Member) -> some View {
        VStack(spacing: 8) {
            // 头像
            Circle()
                .fill(selectedMember?.objectID == member.objectID ? DesignSystem.Colors.primary : Color.gray.opacity(0.3))
                .frame(width: 50, height: 50)
                .overlay(
                    Text(String(member.displayName.prefix(1)))
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.white)
                )
            
            // 姓名
            Text(member.displayName)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .lineLimit(1)
            
            // 积分
            Text("\(Int(member.points))积分")
                .font(.system(size: 10, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(selectedMember?.objectID == member.objectID ? DesignSystem.Colors.primary.opacity(0.1) : Color.clear)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(selectedMember?.objectID == member.objectID ? DesignSystem.Colors.primary : Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
        .onTapGesture {
            selectedMember = member
            testMessage = "已选择成员: \(member.displayName)"
        }
    }
    
    // MARK: - Test Buttons Section
    
    private var testButtonsSection: some View {
        VStack(spacing: 16) {
            Text("测试功能")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            VStack(spacing: 12) {
                // 打开盲盒页面
                testButton(
                    title: "打开盲盒页面",
                    subtitle: "测试完整的盲盒功能",
                    icon: "shippingbox.fill",
                    color: .blue,
                    action: {
                        guard selectedMember != nil else {
                            testMessage = "请先选择一个成员"
                            return
                        }
                        showBlindBox = true
                    }
                )
                
                // 创建测试配置
                testButton(
                    title: "创建测试配置",
                    subtitle: "为选中成员创建盲盒配置",
                    icon: "gear.circle.fill",
                    color: .green,
                    action: createTestConfig
                )
                
                // 添加测试积分
                testButton(
                    title: "添加测试积分",
                    subtitle: "为选中成员添加100积分",
                    icon: "star.circle.fill",
                    color: .orange,
                    action: addTestPoints
                )
                
                // 重置测试数据
                testButton(
                    title: "重置测试数据",
                    subtitle: "清除选中成员的盲盒配置",
                    icon: "trash.circle.fill",
                    color: .red,
                    action: resetTestData
                )
            }
        }
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private func testButton(
        title: String,
        subtitle: String,
        icon: String,
        color: Color,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // 图标
                Image(systemName: icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(color)
                    .frame(width: 30)
                
                // 文字
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text(subtitle)
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
                
                // 箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.05))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Test Result Section
    
    private var testResultSection: some View {
        VStack(spacing: 8) {
            Text("测试结果")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Text(testMessage)
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.green.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.green.opacity(0.3), lineWidth: 1)
                        )
                )
        }
    }
    
    // MARK: - Test Actions
    
    private func createTestConfig() {
        guard let member = selectedMember else {
            testMessage = "请先选择一个成员"
            return
        }
        
        let testPrizes = ["小红花", "贴纸", "铅笔", "橡皮", "尺子"]
        
        let success = dataManager.saveBlindBoxConfig(
            for: member,
            boxCount: 5,
            costPerPlay: 10,
            boxPrizes: testPrizes
        )
        
        if success != nil {
            testMessage = "成功为 \(member.displayName) 创建盲盒配置"
        } else {
            testMessage = "创建盲盒配置失败"
        }
    }
    
    private func addTestPoints() {
        guard let member = selectedMember else {
            testMessage = "请先选择一个成员"
            return
        }
        
        let newPoints = Int(member.points) + 100
        dataManager.updateMemberPoints(member, newPoints: newPoints)
        testMessage = "成功为 \(member.displayName) 添加100积分，当前积分: \(newPoints)"
    }
    
    private func resetTestData() {
        guard let member = selectedMember else {
            testMessage = "请先选择一个成员"
            return
        }
        
        // 删除盲盒配置
        if let config = dataManager.getBlindBoxConfig(for: member) {
            dataManager.viewContext.delete(config)
            dataManager.save()
            testMessage = "成功重置 \(member.displayName) 的盲盒配置"
        } else {
            testMessage = "\(member.displayName) 没有盲盒配置需要重置"
        }
    }
}

#Preview {
    BlindBoxTestView()
        .environmentObject(DataManager.shared)
}
